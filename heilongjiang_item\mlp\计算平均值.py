import pandas as pd


def calculate_averages(file_path, columns):
    try:
        # 读取 Excel 文件
        df = pd.read_excel(file_path)

        # 用于存储每列的平均值
        averages = {}
        for column in columns:
            if column in df.columns:
                # 计算指定列的平均值
                average = df[column].mean()
                averages[column] = average
            else:
                print(f"列 {column} 不存在于 Excel 文件中。")

        return averages
    except FileNotFoundError:
        print(f"未找到文件: {file_path}")
    except Exception as e:
        print(f"发生错误: {e}")


# 示例用法
file_path = r'C:\Users\<USER>\Desktop\mlp\30-10.xlsx'
# 指定要计算平均值的列名
columns = ['MSE', 'SMAPE']

# 调用函数计算平均值
result = calculate_averages(file_path, columns)

# 输出结果
if result:
    for column, average in result.items():
        print(f"{column} 列的平均值是: {average}")