import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
import os


# 数据加载与预处理
high_temp_path = r'C:\Users\<USER>\Desktop\pythonProject1\原始数据\处理后的maxTemp.csv'  # 替换为您的高温数据文件路径
low_temp_path = r'C:\Users\<USER>\Desktop\pythonProject1\原始数据\处理后的minTemp.csv'    # 替换为您的低温数据文件路径

# 检查文件是否存在
if not os.path.exists(high_temp_path):
    raise FileNotFoundError(f"高温数据文件未找到: {high_temp_path}")
if not os.path.exists(low_temp_path):
    raise FileNotFoundError(f"低温数据文件未找到: {low_temp_path}")

# 加载数据
high_df = pd.read_csv(high_temp_path, header=None)  # 假设CSV文件没有表头
low_df = pd.read_csv(low_temp_path, header=None)

# 检查数据形状
print(f"高温数据形状: {high_df.shape}")  # 期望形状为 (4314, 20)
print(f"低温数据形状: {low_df.shape}")   # 期望形状为 (4314, 20)

# 确保两个CSV文件具有相同的行数
assert high_df.shape[0] == low_df.shape[0], "高温和低温数据行数不一致!"

# 选择需要的特征
# 假设每个CSV文件的所有20列都是时间序列特征，您需要根据实际情况选择哪些列表示不同时间步
# 如果每列代表一个时间步，或者每行代表一个时间步，请根据实际情况调整

# 这里我们仅使用所有20列的平均值作为每个时间步的高温和低温值
# 您可以根据实际需求选择具体的特征
high_temps = high_df.mean(axis=1).values  # 形状: (4314,)
low_temps = low_df.mean(axis=1).values    # 形状: (4314,)

# 标准化数据（对高温和低温分别进行标准化）
scaler_high = StandardScaler()
scaler_low = StandardScaler()

high_temps = scaler_high.fit_transform(high_temps.reshape(-1, 1)).flatten()
low_temps = scaler_low.fit_transform(low_temps.reshape(-1, 1)).flatten()

# 合并高温和低温数据为一个数据数组，形状为 (4314, 2)
data = np.stack([high_temps, low_temps], axis=1)
print(f"合并后的数据形状: {data.shape}")  # 期望形状为 (4314, 2)

# 定义自定义 Dataset
class TemperatureDataset(Dataset):
    def __init__(self, data, window_size, predict_step=1):
        self.data = data
        self.window_size = window_size
        self.predict_step = predict_step
        self.num_samples = data.shape[0] - window_size - predict_step + 1

    def __len__(self):
        return self.num_samples

    def __getitem__(self, idx):
        seq = self.data[idx:idx + self.window_size]
        target = self.data[idx + self.window_size + self.predict_step - 1, 0]  # 最高温度

        high_temps = seq[:, 0].reshape(self.window_size, 1)
        low_temps = seq[:, 1].reshape(self.window_size, 1)

        sequence = np.concatenate([high_temps, low_temps], axis=1)  # (window_size, 2)
        sequence = sequence.reshape(self.window_size, 2, 1, 1)    # (window_size, 2, 1, 1)

        sequence = torch.tensor(sequence, dtype=torch.float32)
        target = torch.tensor(target, dtype=torch.float32)

        return sequence, target


# 定义 ConvLSTMCell 和 ConvLSTM 类
class ConvLSTMCell(nn.Module):
    def __init__(self, input_channels, hidden_channels, kernel_size):
        super(ConvLSTMCell, self).__init__()
        assert hidden_channels % 2 == 0, "hidden_channels 必须是2的倍数"

        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.kernel_size = kernel_size
        self.padding = kernel_size // 2

        self.Wxi = nn.Conv2d(self.input_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=True)
        self.Whi = nn.Conv2d(self.hidden_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=False)
        self.Wxf = nn.Conv2d(self.input_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=True)
        self.Whf = nn.Conv2d(self.hidden_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=False)
        self.Wxc = nn.Conv2d(self.input_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=True)
        self.Whc = nn.Conv2d(self.hidden_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=False)
        self.Wxo = nn.Conv2d(self.input_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=True)
        self.Who = nn.Conv2d(self.hidden_channels, self.hidden_channels, self.kernel_size, 1, self.padding, bias=False)

        self.Wci = None
        self.Wcf = None
        self.Wco = None

    def forward(self, x, h, c):
        if self.Wci is None:
            self.Wci = nn.Parameter(torch.zeros(1, self.hidden_channels, 1, 1)).to(x.device)
            self.Wcf = nn.Parameter(torch.zeros(1, self.hidden_channels, 1, 1)).to(x.device)
            self.Wco = nn.Parameter(torch.zeros(1, self.hidden_channels, 1, 1)).to(x.device)

        ci = torch.sigmoid(self.Wxi(x) + self.Whi(h) + c * self.Wci)
        cf = torch.sigmoid(self.Wxf(x) + self.Whf(h) + c * self.Wcf)
        cc = cf * c + ci * torch.tanh(self.Wxc(x) + self.Whc(h))
        co = torch.sigmoid(self.Wxo(x) + self.Who(h) + cc * self.Wco)
        ch = co * torch.tanh(cc)
        return ch, cc

    def init_hidden(self, batch_size, hidden, shape):
        h = torch.zeros(batch_size, hidden, shape[0], shape[1]).to(next(self.parameters()).device)
        c = torch.zeros(batch_size, hidden, shape[0], shape[1]).to(next(self.parameters()).device)
        return (h, c)


class ConvLSTM(nn.Module):
    def __init__(self, input_channels, hidden_channels, kernel_size, step=1, effective_step=[1]):
        super(ConvLSTM, self).__init__()
        self.input_channels = [input_channels] + hidden_channels
        self.hidden_channels = hidden_channels
        self.kernel_size = kernel_size
        self.num_layers = len(hidden_channels)
        self.step = step
        self.effective_step = effective_step
        self._all_layers = []

        for i in range(self.num_layers):
            name = f'cell{i}'
            cell = ConvLSTMCell(self.input_channels[i], self.hidden_channels[i], self.kernel_size)
            setattr(self, name, cell)
            self._all_layers.append(cell)

    def forward(self, input):
        """
        input: (batch, time_steps, channels, height, width)
        """
        internal_state = []
        outputs = []

        for step in range(self.step):
            x = input[:, step, :, :, :]  # shape: (batch, channels, height, width)
            for i in range(self.num_layers):
                name = f'cell{i}'
                cell = getattr(self, name)

                if step == 0:
                    (h, c) = cell.init_hidden(batch_size=x.size(0), hidden=cell.hidden_channels, shape=(x.size(2), x.size(3)))
                    internal_state.append((h, c))

                # 前向传播
                (h, c) = internal_state[i]
                h, c = cell(x, h, c)
                internal_state[i] = (h, c)
                x = h  # 下一层的输入是当前层的隐藏状态

            # 仅记录有效步骤
            if step in self.effective_step:
                outputs.append(x)

        return outputs, (x, c)


class TemperatureConvLSTMModel(nn.Module):
    def __init__(self, input_channels, hidden_channels, kernel_size, step, effective_step):
        super(TemperatureConvLSTMModel, self).__init__()
        self.convlstm = ConvLSTM(input_channels=input_channels, hidden_channels=hidden_channels,
                             kernel_size=kernel_size, step=step, effective_step=effective_step)
        self.output_conv = nn.Conv2d(hidden_channels[-1], 1, kernel_size=1)  # 将最后一个隐藏通道映射到1个输出通道

    def forward(self, x):
        # x: (batch, time_steps, channels, height, width)
        outputs, (last_output, last_cell) = self.convlstm(x)
        output = outputs[0]  # 假设只记录了最后一个有效时间步的输出
        prediction = self.output_conv(output).squeeze(1)  # 形状: (batch, height, width)
        return prediction


# 超参数设置
window_size = 14
predict_step = 7
batch_size = 16
num_epochs = 5
learning_rate = 1e-3

# 创建数据集和数据加载器
dataset = TemperatureDataset(data, window_size=window_size, predict_step=predict_step)
dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

# 初始化模型参数
input_channels = 2  # 高温和低温
hidden_channels = [64, 32]  # 可以根据需要调整
kernel_size = 3
step = window_size
effective_step = [window_size - 1]  # 只记录最后一个时间步的输出

# 创建模型实例
model = TemperatureConvLSTMModel(input_channels=input_channels, hidden_channels=hidden_channels,
                             kernel_size=kernel_size, step=step, effective_step=effective_step).cuda()

# 定义损失函数和优化器
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)


# 定义SMAPE计算函数
def smape_loss(y_pred, y_true):
    epsilon = 1e-8  # 避免除以零
    return torch.mean(torch.abs(y_pred - y_true) / (torch.abs(y_true) + torch.abs(y_pred) + epsilon))


# 训练循环
for epoch in range(num_epochs):
    model.train()
    epoch_loss = 0.0
    epoch_mse_list = [0.0] * predict_step  # 存储每个预测步的MSE
    epoch_smape_list = [0.0] * predict_step  # 存储每个预测步的SMAPE
    for sequences, targets in dataloader:
        sequences = sequences.cuda()  # 形状: (batch, window_size, 2, 1, 1)
        targets = targets.cuda()        # 形状: (batch,)

        optimizer.zero_grad()
        predictions = model(sequences)  # 形状: (batch, height, width)
        predictions = predictions.view(-1)  # 将预测结果展平为 (batch,)

        loss = criterion(predictions, targets)
        mse = loss.item()
        smape = smape_loss(predictions, targets).item()

        loss.backward()
        optimizer.step()

        epoch_loss += loss.item()

        # 计算预测的1-7天每一步的MSE和SMAPE
        for i in range(1, predict_step + 1):
            if i - 1 < len(targets):  # 新增检查
                target = targets[i - 1]
                prediction = predictions[i - 1]
                mse_step = torch.mean((prediction - target) ** 2).item()
                smape_step = smape_loss(prediction, target).item()
                epoch_mse_list[i - 1] += mse_step
                epoch_smape_list[i - 1] += smape_step

    avg_loss = epoch_loss / len(dataloader)
    avg_mse_list = [mse / len(dataloader) for mse in epoch_mse_list]
    avg_smape_list = [smape / len(dataloader) for smape in epoch_smape_list]
    print(f"Epoch [{epoch+1}/{num_epochs}], Loss: {avg_loss:.4f}")
    for i in range(predict_step):
        print(f"  Predict Step [{i+1}], MSE: {avg_mse_list[i]:.4f}, SMAPE: {avg_smape_list[i]:.4f}")

    # 推理与可视化
    model.eval()
    with torch.no_grad():
        # 获取一个批次的数据
        for sequences, targets in dataloader:
            sequences = sequences.cuda()
            targets = targets.cuda()
            predictions = model(sequences)
            predictions = predictions.view(-1, 2).cpu().numpy()  # 假设模型输出形状为 (batch, 2)，其中2代表maxtemp和mintemp
            targets = targets.cpu().numpy().reshape(-1, 2)  # 假设targets形状为 (batch, 2)

            # 反标准化
            predictions_maxtemp = scaler_high.inverse_transform(predictions[:, 0].reshape(-1, 1)).flatten() 
            predictions_mintemp = scaler_low.inverse_transform(predictions[:, 1].reshape(-1, 1)).flatten()  
            targets_maxtemp = scaler_high.inverse_transform(targets[:, 0].reshape(-1, 1)).flatten()  
            targets_mintemp = scaler_low.inverse_transform(targets[:, 1].reshape(-1, 1)).flatten()  
 
            # 可视化前100个样本的maxtemp预测值与真实值 
            plt.figure(figsize=(12, 6))  
            plt.plot(predictions_maxtemp[:100], label='Predicted Max Temp', marker='o')   
            plt.plot(targets_maxtemp[:100], label='True Max Temp', marker='x')   
            plt.title('Predicted vs True Max Temp (First 7 Samples)') 
            plt.xlabel('Sample No')   
            plt.ylabel('Max Temp')   
            plt.legend()    
            plt.show()    
 
            # 可视化前100个样本的mintemp预测值与真实值 
            plt.figure(figsize=(12, 6))     
            plt.plot(predictions_mintemp[:100], label='Predicted Min Temp', marker='o')     
            plt.plot(targets_mintemp[:100], label='True Min Temp', marker='x')     
            plt.title('Predicted vs True Min Temp (First 7 Samples)') 
            plt.xlabel('Sample No')       
            plt.ylabel('Min Temp')       
            plt.legend()       
            plt.show()       
 
            # 确保只处理一个批次的数据 
            break        
 代码实现的功能是预测最高水位最低水温的模型，现在请根据我的需要修改上面的代码完成对水位的预测： 
1.之前实现的是对最高水位最低水温的同时预测，现在我只需要保留其中的最高水温预测，并重新命名为用于水位预测的卷积模型。
 2.我现在想遍历整个文件夹内的所有csv文件，这些csv文件为一些地区的拼音，如fuyuan.csv，每读取到一个csv文件，生成预测值和真实值的对比图，名称为拼音加上比较的后缀，如fuyuan_comparison.png，同时将生成的smape和mse保存到一个excel表格内，文件名称同理。保存到我可以自定义的路径下，并给出指示
3.请注意我的csv文件的数据格式为第一列的列名为“日期”，第二列的列名为“水位值”，我要读取第二列的数据来进行实验。采用7条数据预测3条数据，将预测的数据和csv文件的数据进行相比之下，例如我读取到第 100 条到 106 条数据，预测出第107条109这三条数据，将预测的这三条数据和实际的第107条到109的数据进行对比，计算每一组它们之间的smape和mse，并绘出第三天预测值和真实值的对比图。在七次数据预测后面三条数据的前提下，生成测试集预测出的第三条数据的对比图，假设进行有了200次预测，则对比图应该出现200次第三条预测数据的对比图。对比图和smape、mse的保存路径一致
4.设置数据的前百分之八十数据用来做测试，后面百分之二十的数据用来做预测，对于训练和测试的数据读取，采取训练滑动窗口的模式进行，例如在过程中，第一次读取了1-7条数据，预测8-10的数值，第二次训练会读取2-8条数据，并预测9-11的数值，依次按照“滑动窗口”原理刚性处理数据，按照数据的总数处理：按训练集利用前百分之八十的数据，测试集利用百分之二十的剩余数据进行处理。
