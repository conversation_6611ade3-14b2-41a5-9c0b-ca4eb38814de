# -*- coding: utf-8 -*-
import math

import numpy as np
from pandas import read_csv
from scipy import linalg
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_absolute_percentage_error, mean_squared_error

# from module import *
np.random.seed(42)
from tqdm import tqdm
import os
np.seterr(divide='ignore',invalid='ignore')
class ESN(object):
    """
    创新点：

    """

    def __init__(self, resSize, rho, cr, leaking_rate, X, W=None):
        """
        :param resSize: reservoir size
        :param rho: spectral radius
        :param cr: connectivity ratio
        :param leaking_rate: leaking rate
        :param W: predefined ESN reservoir
        """
        self.resSize = resSize
        self.leaking_rate = leaking_rate

        if W is None:
            # generate the ESN reservoir
            N = resSize * resSize
            # W = np.random.rand(N) - 0.5
            # X_avier
            W = (np.random.rand(N) - 0.5) / math.sqrt(len(X))
            zero_index = np.random.permutation(N)[int(N * cr * 1.0):]
            W[zero_index] = 0
            W = W.reshape((self.resSize, self.resSize))
            rhoW = max(abs(linalg.eig(W)[0]))
            W *= rho / rhoW
            # X_avierMethod：internal weight
            self.W = W.max()
            if self.W > 1:
                self.W = self.W / W
            if self.W.any() < 1:
                self.W = self.W * W
        else:
            assert W.shape[0] == W.shape[1] == resSize, "reservoir size mismatch"
        self.W = W

    def __init_states__(self, X, initLen, reset_state=True):
        self.S = np.zeros((len(X) - initLen, 1 + self.inSize + self.resSize))
        if reset_state:
            self.s = np.zeros(self.resSize)
        s = self.s.copy()
        #  =====EDDM reservoir state=====
        ER = []
        ERR = []
        SD = []
        # t行数，v行矩阵
        for t, u in enumerate(X):
            # ======reservior state======
            if t == 0:
                s = (1 - self.leaking_rate) * s + self.leaking_rate * \
                    np.tanh(np.dot(self.Win, np.hstack((1, u)).astype(float)))
                ER.append(s)
            else:
                s = (1 - self.leaking_rate) * s + self.leaking_rate * \
                    np.tanh(np.dot(self.Win, np.hstack((1, u)).astype(float)) + \
                            np.dot(self.W, s))
                ER.append(s)
                ER_T = (np.sum(abs(ER[t] - ER[t - 1])) / (t)) * 10
                ERR.append(ER_T)
                sd_t = np.std(ERR)
                SD.append(sd_t)
                SD_max = max(SD)
                ER_max = max(ERR)
                if (ER_T + 2 * sd_t) > (2 * SD_max + ER_max):
                    SD_max = sd_t
                    ER_max = ER_T
                eddm = (ER_T + 2 * sd_t) / (ER_max + 2 * SD_max)
                if eddm <= 0.9:
                    s = np.tanh(np.dot(self.Win, np.hstack((1, u))) + \
                                np.dot(self.W, s))
                else:
                    s = (1 - self.leaking_rate) * s + self.leaking_rate * \
                        np.tanh(np.dot(self.Win, np.hstack((1, u))) + \
                                np.dot(self.W, s))

            if t >= initLen:
                self.S[t - initLen] = np.hstack((1, u, s))
        if reset_state:
            self.s = s

    def EDDM_fit(self, X, y, lmbd=1e-8, initLen=20, init_states=True):
        """
        :param X: 1- or 2-dimensional array-like, shape (t,) or (t, d), where
        :         t - length of time series, d - dimensionality.
        :param y : array-like, shape (t,). Target vector relative to X.
        :param lmbd: regularization lambda
        :param initLen: Number of samples to wash out the initial random state
        :param init_states: False allows skipping states initialization if
        :                   it was initialized before (with same X).
        :                   Useful in experiments with different targets.
        """
        assert len(X) == len(y), "input lengths mismatch."
        self.inSize = 1 if np.ndim(X) == 1 else X.shape[1]
        if init_states:
            # X_avier方法 Win
            self.Win = (np.random.rand(self.resSize, self.inSize + 1) - 0.5) * 1 / math.sqrt(len(X))
            winmax = self.Win.max()
            if winmax > 1:
                self.Win = self.Win / winmax
            if winmax < 1:
                self.Win = self.Win * winmax
            self.__init_states__(X, initLen)
        self.ridge = Ridge(alpha=lmbd, fit_intercept=False,
                           solver='svd', tol=1e-6)
        self.__init_states__(X, 0, reset_state=False)
        return self.S

    def evaluate(self, esn_pre, xx):
        errorLen = len(xx)
        #MSE
        esn_err = mean_squared_error(xx[0:errorLen], esn_pre[0:errorLen])
        # smape误差
        # esn_err = (np.mean(np.abs(esn_pre[0:errorLen] - xx[0:errorLen]) / (
        #         np.abs(esn_pre[0:errorLen]) + np.abs(xx[0:errorLen])))) * 100
        return esn_err