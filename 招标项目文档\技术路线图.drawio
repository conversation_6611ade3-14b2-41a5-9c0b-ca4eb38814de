<mxfile host="Electron" modified="2024-10-15T01:56:28.973Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="n3W0tnq8VGXoazStt31W" version="24.2.5" type="device">
  <diagram name="Page-1" id="aaaa8250-4180-3840-79b5-4cada1eebb92">
    <mxGraphModel dx="1532" dy="857" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-2" target="LZW6zN1Tj-yR64-o1Pp3-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-2" value="&lt;font style=&quot;font-size: 15px;&quot;&gt;数据收集与处理&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="140" y="30" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-57" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-6" target="LZW6zN1Tj-yR64-o1Pp3-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-74" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-6" target="LZW6zN1Tj-yR64-o1Pp3-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-6" value="&lt;font style=&quot;font-size: 15px;&quot;&gt;LSTM模型的构建&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-8" target="LZW6zN1Tj-yR64-o1Pp3-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-65" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-8" target="LZW6zN1Tj-yR64-o1Pp3-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-8" value="模型的训练与验证" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="140" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-10" target="LZW6zN1Tj-yR64-o1Pp3-33">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-79" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-10" target="LZW6zN1Tj-yR64-o1Pp3-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-10" value="模型平台部署" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="140" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-17" value="利用pandas、numpy等对数据进行归一化处理和数据修补" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="30" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-22" value="分别利用Adam优化器和粒子群算法提高LSTM和灰色GMP模型的参数精度和预测能力" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="280" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-58" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-24" target="LZW6zN1Tj-yR64-o1Pp3-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-24" value="使用Kaggle的API将数据集从本地上传到平台" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-100" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-28" value="先利用PyTorch保存模型的参数和结构并转换为ONNX格式" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-100" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-32" value="数据备份存储" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="140" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-33" value="利用matplotlib、pycharts对预测数据进行可视化处理" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-37" value="多平台部署" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry y="410" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-38" value="&amp;nbsp; &amp;nbsp; &amp;nbsp; 收集模型结构和权重参数" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry y="340" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-72" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="LZW6zN1Tj-yR64-o1Pp3-49">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="200" y="130" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="200" y="130" />
              <mxPoint x="280" y="130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-75" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-49" target="LZW6zN1Tj-yR64-o1Pp3-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-76" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-49" target="LZW6zN1Tj-yR64-o1Pp3-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-49" value="&lt;span style=&quot;font-size: 15px;&quot;&gt;灰色GMP模型的构建&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="220" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-55" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-53" target="LZW6zN1Tj-yR64-o1Pp3-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-53" value="利用Keras库构建LSTM模型" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-100" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-59" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-28" target="LZW6zN1Tj-yR64-o1Pp3-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-25" y="458" as="sourcePoint" />
            <mxPoint x="25" y="408" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-40" y="438" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-64" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="LZW6zN1Tj-yR64-o1Pp3-28">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="370" as="sourcePoint" />
            <mxPoint x="220" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="LZW6zN1Tj-yR64-o1Pp3-78" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="LZW6zN1Tj-yR64-o1Pp3-2" target="LZW6zN1Tj-yR64-o1Pp3-6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="140" y="370" as="sourcePoint" />
            <mxPoint x="190" y="320" as="targetPoint" />
            <Array as="points">
              <mxPoint x="200" y="130" />
              <mxPoint x="120" y="130" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
