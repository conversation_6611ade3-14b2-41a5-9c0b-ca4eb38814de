import warnings
warnings.filterwarnings("ignore", category=FutureWarning)

import os
import numpy as np
import pandas as pd
import matplotlib
import matplotlib.pyplot as plt

from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import ParameterGrid, train_test_split

import torch
import torch.nn as nn
from torch import optim
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm

# 设置支持中文的字体
matplotlib.rcParams['font.family'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False


# 计算 SMAPE（对称平均百分比误差）
def smape(y_true, y_pred, epsilon=1e-10):
    numerator = 2 * np.abs(y_pred - y_true)
    denominator = np.abs(y_true) + np.abs(y_pred) + epsilon
    return 100 * np.mean(numerator / denominator)


# 自定义数据集
class CustomDataset(Dataset):
    def __init__(self, X, y):
        self.X = torch.tensor(X, dtype=torch.float32)
        self.y = torch.tensor(y, dtype=torch.float32)

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]


# 定义 MLP 模型
class MLP(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(MLP, self).__init__()
        self.mlp = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, output_size)
        )

    def forward(self, x):
        return self.mlp(x)


# 准备时间序列数据：滑动窗口
def prepare_time_series_data(series, input_days, output_days):
    """
    series: shape (N, 1) 的时间序列
    input_days: 滑动窗口输入长度
    output_days: 预测天数
    返回 X, y
      X: (样本数, input_days)
      y: (样本数, output_days)
    """
    X, y = [], []
    for i in range(len(series) - input_days - output_days + 1):
        X.append(series[i : i + input_days].flatten())
        y.append(series[i + input_days : i + input_days + output_days].flatten())
    return np.array(X), np.array(y)


# 绘制预测与真实值对比图：仅绘制第3天(day=2)
def plot_selected_day(y_true, y_pred, station_name, day, save_path):
    plt.figure(figsize=(8, 4))
    plt.plot(y_true[:, day], label='真实值', color='blue')
    plt.plot(y_pred[:, day], label='预测值', color='red')
    plt.title(f'{station_name} - 第{day + 1}天预测与真实值对比')
    plt.xlabel('样本编号')
    plt.ylabel('水位值')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(save_path, f'{station_name}_day_{day + 1}_prediction_vs_true.png'), dpi=300)
    plt.close()


def main():
    # 参数设置
    file_path = r'C:\Users\<USER>\Desktop\mlp\筛选后的水位数据\松花江区域水位数据.xlsx'
    base_save_dir = r'C:\Users\<USER>\Desktop\mlp'
    predictions_dir = os.path.join(base_save_dir, '松花江7_3预测结果')
    model_dir = os.path.join(base_save_dir, 'model/mlp7_3')
    metrics_dir = os.path.join(base_save_dir, '松花江7_3指标结果')
    station_info_file = r'C:\Users\<USER>\Desktop\mlp\水位数据\经纬度.xls'

    # 最优超参数 CSV 路径
    best_params_csv_path = os.path.join(predictions_dir, 'best_hyperparameters_7_3.csv')
    # 所有站点预测（含差值）合并在一个 CSV
    all_stations_predictions_path = os.path.join(predictions_dir, 'all_stations_predictions.csv')

    if not os.path.exists(predictions_dir):
        os.makedirs(predictions_dir)
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    if not os.path.exists(metrics_dir):
        os.makedirs(metrics_dir)

    # 读取站点信息
    station_info_df = pd.read_excel(station_info_file)
    station_info_df.columns = station_info_df.columns.str.strip().str.lower()
    station_code_map = dict(zip(station_info_df['站名'], station_info_df['站码']))

    try:
        xls = pd.ExcelFile(file_path)
        sheet_names = xls.sheet_names
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return

    # 定义超参数网格
    param_grid = {
        'hidden_size': [32, 64],
        'learning_rate': [0.001, 0.0001],
        'batch_size': [16, 32],
        'epochs': [100, 200]
    }
    grid = list(ParameterGrid(param_grid))

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 7 天输入，3 天输出
    input_days = 7
    output_days = 3

    station_count = 0
    best_params_list = []

    # 用于合并所有站点预测结果的全局列表
    all_predictions_list = []

    for sheet_name in sheet_names:
        try:
            print(f"处理站点: {sheet_name}")
            if sheet_name not in station_code_map:
                print(f"站点 {sheet_name} 未在站点信息文件中找到，跳过。")
                continue
            station_code = station_code_map[sheet_name]

            # 读取数据
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            if '水位值' not in df.columns:
                print(f"站点 {sheet_name} 数据中缺少 '水位值' 列，跳过。")
                continue
            water_data = df['水位值'].values.reshape(-1, 1)

            # 归一化
            scaler = MinMaxScaler(feature_range=(0, 1))
            water_norm = scaler.fit_transform(water_data)

            # 使用滑动窗口准备数据
            X_data, y_data = prepare_time_series_data(water_norm, input_days, output_days)

            # 划分训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_data, y_data, test_size=0.2, shuffle=False
            )

            input_size = X_train.shape[1]
            output_size = y_train.shape[1]

            best_mse = float('inf')
            best_smape = float('inf')
            best_params = None
            best_model_state = None
            best_y_pred_denorm = None
            true_denorm = None

            daily_mse_list = []
            daily_smape_list = []

            # 遍历超参数网格
            for params in grid:
                # 数据加载器
                train_dataset = CustomDataset(X_train, y_train)
                train_dataloader = DataLoader(train_dataset, batch_size=params['batch_size'], shuffle=True)
                val_dataset = CustomDataset(X_test, y_test)
                val_dataloader = DataLoader(val_dataset, batch_size=params['batch_size'], shuffle=False)

                # 初始化模型
                model = MLP(input_size, params['hidden_size'], output_size).to(device)
                optimizer = optim.Adam(model.parameters(), lr=params['learning_rate'])
                criterion = nn.MSELoss()

                # 训练
                for epoch in range(params['epochs']):
                    model.train()
                    for x_batch, y_batch in train_dataloader:
                        x_batch = x_batch.to(device)
                        y_batch = y_batch.to(device)
                        optimizer.zero_grad()
                        outputs = model(x_batch)
                        loss = criterion(outputs, y_batch)
                        loss.backward()
                        optimizer.step()

                # 评估
                model.eval()
                preds = []
                truths = []
                with torch.no_grad():
                    for x_batch, y_batch in val_dataloader:
                        x_batch = x_batch.to(device)
                        outputs = model(x_batch)
                        preds.append(outputs.cpu().numpy())
                        truths.append(y_batch.numpy())

                y_pred = np.vstack(preds)
                y_true = np.vstack(truths)

                mse = mean_squared_error(y_true, y_pred)
                current_smape = smape(y_true, y_pred)

                # 计算每一天的 MSE 和 SMAPE
                daily_mse = []
                daily_smape = []
                for day in range(output_days):
                    day_mse = mean_squared_error(y_true[:, day], y_pred[:, day])
                    day_smape = smape(y_true[:, day], y_pred[:, day])
                    daily_mse.append(day_mse)
                    daily_smape.append(day_smape)

                if mse < best_mse:
                    best_mse = mse
                    best_smape = current_smape
                    best_params = params
                    best_model_state = model.state_dict().copy()

                    # 反归一化预测 & 真实值
                    best_y_pred_denorm = scaler.inverse_transform(y_pred.reshape(-1, 1)).reshape(y_pred.shape)
                    true_denorm = scaler.inverse_transform(y_true.reshape(-1, 1)).reshape(y_true.shape)

                    daily_mse_list = daily_mse
                    daily_smape_list = daily_smape

            if best_model_state is None:
                print(f"站点 {sheet_name} 未找到合适的模型。")
                continue

            print(f"站点: {sheet_name} 最佳参数: {best_params}, 最低MSE: {best_mse}, SMAPE: {best_smape}")
            best_params_list.append({
                'Station Name': sheet_name,
                'Station Code': station_code,
                'Hidden Size': best_params['hidden_size'],
                'Learning Rate': best_params['learning_rate'],
                'Batch Size': best_params['batch_size'],
                'Epochs': best_params['epochs'],
                'MSE': best_mse,
                'SMAPE': best_smape,
                **{f'Day {i + 1} MSE': daily_mse_list[i] for i in range(output_days)},
                **{f'Day {i + 1} SMAPE': daily_smape_list[i] for i in range(output_days)}
            })

            # 保存最佳模型
            best_model_path = os.path.join(model_dir, f"{station_code}_7_3_best.pth")
            torch.save(best_model_state, best_model_path)
            print(f"{sheet_name} 的最佳模型已保存到 {best_model_path}")

            # 差值
            if best_y_pred_denorm is not None and true_denorm is not None:
                difference = best_y_pred_denorm - true_denorm
            else:
                difference = None

            # 构建预测结果 DataFrame
            station_predictions_df = pd.DataFrame(best_y_pred_denorm,
                                                 columns=[f'Day {i + 1}' for i in range(output_days)])
            # 在第一列插入站点名称
            station_predictions_df.insert(0, 'Station Name', sheet_name)

            # 每天差值列
            if difference is not None:
                for i in range(output_days):
                    station_predictions_df[f'Day {i + 1} Diff'] = difference[:, i]

            # 将本站点的预测结果加入全局列表，待合并
            all_predictions_list.append(station_predictions_df)

            # 可视化第3天预测（day=2）
            if best_y_pred_denorm is not None and true_denorm is not None:
                station_dir = os.path.join(predictions_dir, str(sheet_name))
                if not os.path.exists(station_dir):
                    os.makedirs(station_dir)
                plot_selected_day(true_denorm, best_y_pred_denorm, sheet_name, day=2, save_path=station_dir)

            # 保存每一天的 MSE 和 SMAPE 到 CSV
            metrics_df = pd.DataFrame({
                'Day': [i + 1 for i in range(output_days)],
                'MSE': daily_mse_list,
                'SMAPE': daily_smape_list
            })
            metrics_csv_path = os.path.join(metrics_dir, f"{station_code}_7_3_metrics.csv")
            metrics_df.to_csv(metrics_csv_path, index=False, encoding='utf-8-sig')
            print(f"{sheet_name} 的每日指标已保存到 {metrics_csv_path}")

            station_count += 1

        except Exception as e:
            print(f"处理 {sheet_name} 时出错: {str(e)}")
            continue

    # ---------- 在此合并并保存所有站点的预测结果到一个 CSV -----------
    if len(all_predictions_list) > 0:
        all_predictions_df = pd.concat(all_predictions_list, ignore_index=True)
        all_predictions_df.to_csv(all_stations_predictions_path, index=False, encoding='utf-8-sig')
        print(f"所有站点的预测结果已统一保存到 {all_stations_predictions_path}")
    else:
        print("没有任何站点的预测结果被写入。")

    # 保存所有站点的最佳超参数和指标
    best_params_df = pd.DataFrame(best_params_list)
    best_params_df.to_csv(best_params_csv_path, index=False, encoding='utf-8-sig')
    print(f"所有站点的最佳超参数已保存到 {best_params_csv_path}")
    print(f"成功处理的站点数量: {station_count}")


if __name__ == "__main__":
    main()