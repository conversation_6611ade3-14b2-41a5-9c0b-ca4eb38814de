上面代码实现了从station_info_file路径下的本地Excel文件匹配对应站点，根据sheet名读取file_path路径下处理好的数据，进行模型训练，现在我的数据放在了一个数据库中（db_config = {
    'host': '*************',  # 数据库主机地址
    'user': 'root',           # 数据库用户名
    'password': '123123',     # 数据库密码
    'database': 'hlj',        # 要连接的数据库名称
    'port': 3306              # 端口
}）b_water_level表中里面的列分别为:id，station_code，station_name，water_level_date，water_level，stream_flow等，我希望以后的模型训练可以在数据库中根据我提供的日期间隔（格式为:2010-01-01 to 2024-12-31），根据water_level_date读取规定日期内的对应的数据，所有的站点（有唯一的站码station_code、station_name与之对应）的水位数据在数据库存放在water_level列，修改代码完成:对于所有的水位站，读取我规定日期内的水位数据，检验数据的数量和异常值（异常值采用IQR方法检测，检测出异常之后剔除异常值，采用相邻两数据平均值代替，对应连续缺失的异常值使用三次样条插值进行回归拟合填充，读取的数据按日期升序排序用来进行模型训练的输入），将读取数据库水位数据和异常处理这些操作的过程写成一个Excel表格名为Process_result，（每一行都是:station_code station_name  读取到的数据量大小 异常值数量，异常值对应的日期，对于不同的水位站station_name，按照他们的station_name生成不同的sheet表），Excel表格的保存路径可以自定义，其余代码功能保持不变，综上，只是修改代码中读取数据的部分，使地区的数据从数据库水位获取，而不是一个表格，同时由于读取的表格是对数据处理后的表格，而数据库的数据还是原始数据，因此需要在读取数据库处理一下数据，这过程的数据可以生成一个本地文件进行保存，也可以在Python中生成一个列表存放从数据库读取的数据，操作的流程是读取并处理完一个站点后进行模型的训练，然后再读取处理训练，直到所有的水位站都处理完成，对于数据量少于200的水位站，只在Process_result说明该station_name数据量量大小，不处理异常数据。同时将其余部分对应的代码进行修改以匹配新的读取数据变量名称。修改文件的代码，并发一份完整的给我。