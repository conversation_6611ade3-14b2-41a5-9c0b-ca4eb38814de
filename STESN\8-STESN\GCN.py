import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np

# 定义GCN模型
from sklearn.metrics import mean_squared_error


class GCN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(GCN, self).__init__()
        self.gc1 = GraphConvolution(input_dim, hidden_dim)
        self.relu = nn.ReLU()
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x, adj):
        x = self.gc1(x, adj)
        x = self.relu(x)
        x = self.fc(x)
        return x

# 定义图卷积层
class GraphConvolution(nn.Module):
    def __init__(self, input_dim, output_dim):
        super(GraphConvolution, self).__init__()
        # self.weight = nn.Parameter(torch.FloatTensor(input_dim, output_dim))
        # self.bias = nn.Parameter(torch.FloatTensor(output_dim))
        self.weight = nn.Parameter(torch.rand(input_dim, output_dim))
        self.bias = nn.Parameter(torch.rand(output_dim))

    def forward(self, x, adj):
        support = torch.matmul(x, self.weight)
        output = torch.matmul(adj, support) + self.bias
        return output



# 定义训练函数
def train(model, features, adjacency_matrix, labels, epochs, lr):
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)

    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        output = model(features, adjacency_matrix)
        loss = criterion(output, labels)
        loss.backward()
        optimizer.step()

        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch + 1}/{epochs}, Loss: {loss.item()}")


# 定义预测函数
def predict(model, features, adjacency_matrix):
    model.eval()
    with torch.no_grad():
        output = model(features, adjacency_matrix)
    return output


