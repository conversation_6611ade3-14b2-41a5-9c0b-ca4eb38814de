# !/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :main.py
# @Time      :2023/6/8 15:59
# <AUTHOR>
# @desc      :
import os

import pandas as pd
from self_attention import *
from GCN import *
from TESNState import *
from RandomBar_PCA import *
from ESN import *
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset


if __name__ == '__main__':
    filename = '8_ST-ESN-DF_MSE'
    # filename = '8_ST-ESN_SMAPE'
    outputfile = open(
        "D:\\Learning\\04-CODE\\My_paper\\6-MasterThesis\\code\\0-SingleFactorWaterLevel\\Output\\" + filename + '.txt', 'a')

    # load the data
    path = '../Data/'  # 需要修改的文件所在的路径
    original_name = os.listdir(path)
    for i in original_name:  # 遍历全部文件
        print(i)
        data = read_csv('../Data/' + i)
        data = RandomBar_PCA(data.values)
        print(str(i), file=outputfile)
        print("====================", file=(outputfile))

        X = pd.DataFrame(data).iloc[:, :data.shape[1] - 5].values
        Y = pd.DataFrame(data).iloc[:, data.shape[1] - 5:].values
        # 划分训练集和测试集
        x_train = X[0:int(len(X) * 0.7), ]
        x_test = X[int(len(X) * 0.7):len(X), ]
        y_train = Y[0:int(len(X) * 0.7), ]
        y_test = Y[int(len(X) * 0.7):len(X), ]
        # ==============参数调整================================
        leaking_rate = 0.2
        resSize = 80
        # ======esn参数设置======
        dif = 46  # prediction horizon >=1
        rho = 0.5  # spectral radius
        cr = 0.05  # connectivity ratio
        lmbd = 1e-8  # regularization coefficient
        initLen = 200
        print("leaking_rate = " + str(leaking_rate))
        print("resSize = " + str(resSize))

        trainLen = len(x_train)
        testLen = len(x_test)

        esn = ESN(
            resSize=resSize,
            rho=rho,
            cr=cr,
            leaking_rate=leaking_rate,
            X=x_train,
        )

        hidden_dim = 128  # 隐层特征维度
        num_samples = 11887  # 样本数

        # 2. time feature:train
        esn_state_tr = esn.EDDM_fit(x_train, y_train, initLen=initLen, lmbd=lmbd)

        # 3. space feature:train
        # 3.1 self attention --> adjacency matrix
        graph_matrix_tr = np.array(attention(esn_state_tr))
        adj_matrix_tr = torch.FloatTensor(graph_matrix_tr)
        # 3.2 x_train --> feature matrix
        feature_matrix_tr = torch.FloatTensor(x_train)
        labels = torch.FloatTensor(y_train)
        # 3.3 GCN
        model = GCN(feature_matrix_tr.shape[1], hidden_dim, labels.shape[1])
        epochs = 100
        learning_rate = 0.1
        train(model, feature_matrix_tr, adj_matrix_tr, labels, epochs, learning_rate)

        # 2. time feature:test
        esn_state_te = esn.EDDM_fit(x_test, y_test, initLen=initLen, lmbd=lmbd)

        # 3. space feature:test
        # 3.1 self attention --> adjacency matrix
        graph_matrix_te = np.array(attention(esn_state_te))
        # 2.GCN layer
        adj_matrix_tr = torch.FloatTensor(graph_matrix_tr)
        feature_matrix_tr = torch.FloatTensor(x_train)
        graph_tr = predict(model, feature_matrix_tr, adj_matrix_tr).numpy()
        adj_matrix_te = torch.FloatTensor(graph_matrix_te)
        feature_matrix_te = torch.FloatTensor(x_test)
        graph_te = predict(model, feature_matrix_te, adj_matrix_te).numpy()

        Combined_Feature_Tr = np.concatenate((esn_state_tr, graph_tr), axis=1)
        Combined_Feature_Te = np.concatenate((esn_state_te, graph_te), axis=1)

        # 4. predict
        class MLP(nn.Module):
            def __init__(self, input_size, hidden_size, output_size):
                super(MLP, self).__init__()
                self.fc1 = nn.Linear(input_size, hidden_size)  # 全连接层1
                self.fc2 = nn.Linear(hidden_size, output_size)  # 输出层

            def forward(self, x):
                x = torch.relu(self.fc1(x))  # 全连接层1后ReLU激活
                x = self.fc2(x)  # 输出层
                return x


        # 将数据转换为 PyTorch Tensor
        train_dataset = TensorDataset(torch.Tensor(Combined_Feature_Tr), torch.Tensor(y_train))
        test_dataset = TensorDataset(torch.Tensor(Combined_Feature_Te), torch.Tensor(y_test))
        batch_size = 8
        input_size = Combined_Feature_Tr.shape[1]
        hidden_size = 8
        output_size = y_train.shape[1]
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size)

        # 创建模型实例
        model = MLP(input_size=input_size, hidden_size=hidden_size, output_size=output_size)

        # 定义损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)

        # 训练模型
        num_epochs = 100
        for epoch in range(num_epochs):
            model.train()
            for inputs, labels in train_loader:
                optimizer.zero_grad()
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()

        # 使用模型进行预测
        model.eval()
        with torch.no_grad():
            predictions = []
            for inputs, _ in test_loader:
                outputs = model(inputs)
                predictions.append(outputs.numpy())
            predict_te = np.concatenate(predictions)

        for i in range(Y.shape[1]):
            err_final = esn.evaluate(predict_te[i], y_test[i])
            print('MSE = ', err_final, file=outputfile)
            # print('SMAPE = ', err_final, file=outputfile)

    outputfile.close()
