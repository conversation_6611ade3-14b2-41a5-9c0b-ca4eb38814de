import os
import sys
import numpy as np
import pandas as pd
from datetime import timedelta

# 允许从包含连字符的目录名 `OS-ELM` 导入 oselm.py（用于反序列化）
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
OSELM_DIR = os.path.join(CURRENT_DIR, 'OS-ELM')
if OSELM_DIR not in sys.path:
    sys.path.append(OSELM_DIR)

import oselm  # noqa: F401  确保pickle能找到类定义

# -------------------------- 配置区 --------------------------
config = {
    # 本地水位数据Excel路径
    'DATA_PATH': r"E:\黑龙江水位数据_20站点\water_level_data_20100101_to_20250815.xlsx",
    # 预测起始日期（字符串格式）
    'REFERENCE_DATE': '2025-08-01',
    # 仅预测天数（固定为10，对应30→10中期模型）
    'PREDICT_DAYS': 10,
    # 站点列表（可指定部分或全部）
    'REFERENCE_STATIONS': {
        "三姓": "10701800",
        "奇克": "10102200",
        "勤得利": "10103600",
        "佳木斯": "10702000",
        "下岱吉": "10700700",
        "通河": "10701600",
        "虎头": "10500200",
        "齐齐哈尔": "11202220",
        "饶河": "10500400",
        "抚远": "10104000",
        "海青": "10501000"
    },
    # OS-ELM最佳模型目录（由 oselm_30_10_train.py 生成）
    'MODEL_DIR': r'E:\黑龙江水位数据_20站点\oselm30_10_best',
    # 输出预测结果Excel文件名
    'OUTPUT_FILE': 'water_level_predictions_oselm_local.xlsx',
    # 输入-输出窗口配置（OS-ELM 30→10）
    'INPUT_DAYS': 30,
    'OUTPUT_DAYS': 10,
}

# -------------------------- 数据读取 --------------------------
def read_station_data(data_path: str) -> pd.DataFrame:
    df = pd.read_excel(data_path, dtype={'station_code': str})
    df['water_level_date'] = pd.to_datetime(df['water_level_date'])
    return df

# 根据参考日期向前获取最近 n 条非空水位数据（不插值）
def get_last_n_valid_water_data_from_df(df: pd.DataFrame, station_code: str, end_date, n: int = 60) -> pd.DataFrame:
    end_date = pd.to_datetime(end_date)
    sub = df[(df['station_code'] == station_code) & (df['water_level_date'] <= end_date)].copy()
    sub = sub.sort_values('water_level_date')
    sub = sub[sub['water_level'].notna()]
    if len(sub) < n:
        return pd.DataFrame()
    return sub.tail(n)

# -------------------------- OS-ELM模型预测 --------------------------
def find_model_file(model_dir: str, station_code: str) -> str | None:
    if not os.path.isdir(model_dir):
        return None
    # 精确匹配站码开头的pkl文件
    for fname in os.listdir(model_dir):
        if fname.startswith(station_code) and fname.endswith('.pkl'):
            return os.path.join(model_dir, fname)
    # 兼容部分匹配
    for fname in os.listdir(model_dir):
        if station_code in fname and fname.endswith('.pkl'):
            return os.path.join(model_dir, fname)
    return None

def load_oselm_bundle(model_path: str):
    import pickle
    try:
        with open(model_path, 'rb') as f:
            bundle = pickle.load(f)
        return bundle
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None

def predict_for_station(station_code: str, station_name: str, df: pd.DataFrame, cfg: dict):
    reference_date = pd.to_datetime(cfg['REFERENCE_DATE'])
    predict_days = int(cfg['PREDICT_DAYS'])
    input_days = int(cfg['INPUT_DAYS'])
    output_days = int(cfg['OUTPUT_DAYS'])
    model_dir = cfg['MODEL_DIR']

    # 取参考日前最近60条非空数据，保证窗口足够
    water_df = get_last_n_valid_water_data_from_df(df, station_code, reference_date, n=60)
    if water_df.empty or len(water_df) < 60:
        print(f"站点 {station_name}({station_code}) 正常数据不足60条，跳过。")
        return None

    water_levels = water_df['water_level'].values.astype(float)

    # 查找并加载模型
    model_file = find_model_file(model_dir, station_code)
    if not model_file:
        print(f"站点 {station_name}({station_code}) 未找到OS-ELM模型文件，跳过。")
        return None

    bundle = load_oselm_bundle(model_file)
    if bundle is None:
        print(f"站点 {station_name}({station_code}) 模型加载失败，跳过。")
        return None

    # 校验模型参数
    if bundle.get('input_days') != input_days or bundle.get('output_days') != output_days:
        print(f"站点 {station_name}({station_code}) 模型参数不匹配（输入/输出天数），跳过。")
        return None

    model = bundle['model']
    scaler = bundle['scaler']

    try:
        # 使用模型自带scaler归一化最近30天输入
        u_raw = water_levels[-input_days:]
        u_scaled = scaler.transform(u_raw.reshape(-1, 1)).reshape(1, -1)
        # 预测（得到10天）
        y_scaled = model.predict(u_scaled)
        y_pred = scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()
        y_pred = y_pred[:min(output_days, predict_days)]
    except Exception as e:
        print(f"站点 {station_name}({station_code}) 预测失败: {e}")
        return None

    # 生成预测日期（参考日后1~10天）
    prediction_dates = [reference_date + timedelta(days=i + 1) for i in range(len(y_pred))]

    return prediction_dates, y_pred

# -------------------------- 主流程 --------------------------
def main(cfg: dict):
    df = read_station_data(cfg['DATA_PATH'])
    station_items = list(cfg['REFERENCE_STATIONS'].items())
    all_rows = []

    for i, (station_name, station_code) in enumerate(station_items):
        print(f"\n[{i+1}/{len(station_items)}] 预测站点: {station_name}({station_code})")
        result = predict_for_station(station_code, station_name, df, cfg)
        if result is None:
            continue
        pred_dates, preds = result

        # 对齐真实水位并计算差值
        for date, pred in zip(pred_dates, preds):
            real_row = df[(df['station_code'] == station_code) & (df['water_level_date'] == date)]
            if not real_row.empty:
                try:
                    real_val = float(real_row.iloc[0]['water_level'])
                except Exception:
                    real_val = None
            else:
                real_val = None
            diff_val = pred - real_val if real_val is not None else None
            all_rows.append([
                station_name,
                station_code,
                date.strftime('%Y-%m-%d'),
                round(float(pred), 2),
                round(float(real_val), 2) if real_val is not None else None,
                round(float(diff_val), 2) if diff_val is not None else None
            ])

    if all_rows:
        out_df = pd.DataFrame(all_rows, columns=pd.Index(['站点名称', '站点编码', '预测日期', '预测水位', '真实水位', '差值']))
        out_df.to_excel(cfg['OUTPUT_FILE'], index=False)
        print(f"\n预测结果已保存到 {cfg['OUTPUT_FILE']}")
        print(out_df.head())
        # 简要统计
        valid = out_df[out_df['差值'].notna()]
        if not valid.empty:
            print(f"平均差值: {valid['差值'].mean():.2f}，标准差: {valid['差值'].std():.2f}")
    else:
        print("没有有效的预测数据。")

if __name__ == "__main__":
    main(config) 