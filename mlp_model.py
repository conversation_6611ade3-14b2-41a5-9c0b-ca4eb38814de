#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sat Mar 23 15:53:12 2024

@author: kubota
"""


from __future__ import unicode_literals, print_function, division
from io import open
import unicodedata
import string
import re
import random

import torch
import torch.nn as nn
from torch import optim
import torch.nn.functional as F
import pickle
from tqdm import tqdm
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import *
from sklearn.model_selection import train_test_split
import math
import csv
import os
import numpy as np

objs = os.scandir('dataset for electricity loading')

data = []
for entry in objs:    
    with open(entry.path, newline='') as csvfile:
        spamreader = csv.reader(csvfile)
        for row in spamreader:
            data.append(np.asarray(row, dtype=np.float32))

data = np.stack(data)
trainIndex, testIndex = train_test_split(range(len(data)), test_size=0.3, random_state=1)




class CustomDataset(Dataset):
   
    def __init__(self, data, selectedIndex):
        self.data = data[selectedIndex]
        

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        if torch.is_tensor(idx):
            idx = idx.tolist()
       
        return self.data[idx,:18], self.data[idx,:18]
    
    

class MLP(nn.Module):
    def __init__(self, input_size, hidden_size, output_size ):
        super(MLP, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size

        self.mlp = nn.Sequential(
            nn.Linear(input_size , hidden_size ),
            nn.ReLU(),
            nn.Linear(hidden_size , hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size , hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, output_size)
        )
        
    def forward(self, x_input, c = None):      

        y = self.mlp(x_input)
     
        return y
   
    

train_dataset = CustomDataset(data, trainIndex)
train_dataloader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=0)

val_dataset = CustomDataset(data,testIndex)
val_dataloader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=0)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# random.seed(0)
# np.random.seed(0)
# torch.manual_seed(0)
# torch.cuda.manual_seed_all(0)



model = MLP(18,32, 18).to(device)



optimizer = optim.Adam(model.parameters(), lr=0.0001)
criterion = nn.MSELoss()

lossValues = []

bar = tqdm(range(1000))
for epoch in bar:
    model.train()
    
    totalLoss = 0
      
    for x, y in train_dataloader:
        
        input_tensor = x.float().to(device)
        target_tensor = y.float().to(device)        
       
        optimizer.zero_grad()
        outputs = model(input_tensor)                              
            
        loss = criterion(outputs, target_tensor)
        loss.backward()
        optimizer.step()
        totalLoss += loss.item()
    lossValues.append(totalLoss)

    
    bar.set_description("loss:{}".format(totalLoss))
    

model.eval()


y_pred = []
y_true = []
for x, y in val_dataloader:    
    with torch.no_grad():
        input_tensor = x.float().to(device)
        target_tensor = y.float().to(device)    
        
        outputs = model(input_tensor)        
          
        
        y_pred += outputs.detach().cpu().data
        y_true += target_tensor.detach().cpu().data
     
        
y_true = torch.stack(y_true)    
y_pred = torch.stack(y_pred)  




print("mean_squared_error", mean_squared_error(y_true, y_pred))
print("mean_absolute_error",mean_absolute_error(y_true, y_pred))
print("r2_score",r2_score(y_true, y_pred))
print("explained_variance_score",explained_variance_score(y_true, y_pred))
print("mean_pinball_loss 0.1",mean_pinball_loss(y_true, y_pred, alpha=0.1))
print("mean_pinball_loss 0.9",mean_pinball_loss(y_true, y_pred, alpha=0.9))
print("d2_pinball_score 0.1",d2_pinball_score(y_true, y_pred, alpha=0.1))
print("d2_pinball_score 0.9",d2_pinball_score(y_true, y_pred, alpha=0.9))
print("d2_absolute_error_score",d2_absolute_error_score(y_true, y_pred))







