# !/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :selfAttention.py
# @Time      :2022/11/26 15:40
# <AUTHOR>
# @desc      :

from pandas import read_csv
import numpy as np
import math


def attention(X):
    """
            :param N: sampleSize
            :param Q: query
            :param K: key
            """
    # X_avier方法 query
    weight_query = (np.random.rand(X.shape[0], X.shape[1])) / math.sqrt(len(X))
    # querymax = weight_query.max()
    # if querymax > 1:
    #     weight_query = weight_query / querymax
    # if querymax < 1:
    #     weight_query = weight_query * querymax
    # X_avier方法 key
    weight_key = (np.random.rand(X.shape[0], X.shape[1])) / math.sqrt(len(X))
    # keymax = weight_key.max()
    # if keymax > 1:
    #     weight_key = weight_key / keymax
    # if keymax < 1:
    #     weight_key = weight_key * keymax
    # X_avier方法 value
    weight_value = (np.random.rand(X.shape[0], X.shape[1])) / math.sqrt(len(X))
    # valuemax = weight_value.max()
    # if valuemax > 1:
    #     weight_value = weight_value / keymax
    # if keymax < 1:
    #     weight_value = weight_value * keymax
    Q = np.matmul(np.mat(weight_query),np.mat(X).T)
    K = np.matmul(np.mat(weight_key),np.mat(X).T)
    V = np.matmul(np.mat(weight_value),np.mat(X).T)

    att_weights = np.matmul(Q, np.transpose(K)) / np.sqrt(len(Q))
    att_weights = np.exp(att_weights - np.max(att_weights, axis=(-1, -2)))
    att_weights = att_weights / np.sum(att_weights, axis=(-1, -2))

    # apply attention weights to value matrix to get attention output
    att_output = np.matmul(att_weights, V)

    return att_output



