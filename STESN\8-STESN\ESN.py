# !/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :2-ESN_DirectLink.py
# @Time      :2023/5/16 16:49
# <AUTHOR>
# @desc      :
import numpy as np
from pandas import read_csv
from scipy import linalg
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_squared_error

np.random.seed(42)
class ESN_Pr(object):
    """
    创新点：
    recurrent step完成
    """

    def __init__(self, resSize, rho, cr, leaking_rate, W=None):
        """
        :param resSize: reservoir size
        :param rho: spectral radius
        :param cr: connectivity ratio
        :param leaking_rate: leaking rate
        :param W: predefined 1-ESN reservoir
        """
        self.resSize = resSize
        self.leaking_rate = leaking_rate

        if W is None:
            # generate the 1-ESN reservoir
            N = resSize * resSize
            W = np.random.rand(N) - 0.5
            zero_index = np.random.permutation(N)[int(N * cr * 1.0):]
            W[zero_index] = 0
            W = W.reshape((self.resSize, self.resSize))
            rhoW = max(abs(linalg.eig(W)[0]))
            if rhoW != 0:
                W *= rho / rhoW
            else:
                # 处理除以零的情况
                # 例如，可以选择重新初始化 W 或进行其他操作
                pass
        else:
            assert W.shape[0] == W.shape[1] == resSize, "reservoir size mismatch"
        self.W = W

    def __init_states__(self, X, initLen, reset_state=True):
        # allocate memory for the collected states matrix
        self.S = np.zeros((len(X) - initLen, 1 + self.inSize + self.resSize))
        if reset_state:
            self.s = np.zeros(self.resSize)
        s = self.s.copy()
        # run the reservoir with the data and collect S
        for t, u in enumerate(X):
            # ======reservior state================
            s = (1 - self.leaking_rate) * s + self.leaking_rate * \
                np.tanh(np.dot(self.Win, np.hstack((1, u)).astype(float)) + \
                        np.dot(self.W, s))
            if t >= initLen:
                self.S[t - initLen] = np.hstack((1, u, s))
        if reset_state:
            self.s = s
    def fit(self, X, y, Combined_Feature_Tr, lmbd=1e-8, initLen=20, init_states=True):
        """
        :param X: 1- or 2-dimensional array-like, shape (t,) or (t, d), where
        :         t - length of time series, d - dimensionality.
        :param y : array-like, shape (t,). Target vector relative to X.
        :param lmbd: regularization lambda
        :param initLen: Number of samples to wash out the initial random state
        :param init_states: False allows skipping states initialization if
        :                   it was initialized before (with same X).
        :                   Useful in experiments with different targets.
        """
        # assert len(X) == len(y), "input lengths mismatch."
        # self.inSize = 1 if np.ndim(X) == 1 else X.shape[1]
        # if init_states:
        #
        #     self.Win = (np.random.rand(self.resSize, 1 + self.inSize) - 0.5) * 1
        #     self.__init_states__(X, initLen)
        self.ridge = Ridge(alpha=lmbd, fit_intercept=False,
                           solver='svd', tol=1e-6)
        # # direct Link
        # # self.S1 = np.concatenate((X[initLen:], self.S), axis=1)
        ridge = self.ridge.fit(Combined_Feature_Tr, y[initLen:])

        # ridge = self.ridge.fit(self.S, y[initLen:])
        esn_w = ridge.coef_
        # 权重
        # print(self.ridge.coef_)
        # 截距
        # print(self.ridge.intercept_)
        return ridge, esn_w

    def predict(self, X, ridge, graph_state, init_states=True):
        """
        :param X: 1- or 2-dimensional array-like, shape (t) or (t, d)
        :param init_states: see above
        """
        """
                :param X: 1- or 2-dimensional array-like, shape (t) or (t, d)
                :param init_states: see above
                """
        if init_states:
            self.__init_states__(X, 0, graph_state, reset_state=False)
            # direct Link
            # self.S1 = np.concatenate((X, self.S), axis=1)
        # y = ridge.predict(self.S1)
        y = ridge.predict(graph_state)
        return y

    def evaluate(self, esn_pre, xx):
        errorLen = len(xx)
        #MSE
        esn_err = mean_squared_error(xx[0:errorLen], esn_pre[0:errorLen])
        # smape误差
        # esn_err = (np.mean(np.abs(esn_pre[0:errorLen] - xx[0:errorLen]) / (
        #         np.abs(esn_pre[0:errorLen]) + np.abs(xx[0:errorLen])))) * 100
        return esn_err
