# !/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :RandomBar_PCA.py
# @Time      :2023/5/16 16:41
# <AUTHOR>
# @desc      :

import numpy as np
from sklearn.decomposition import PCA
def RandomBar_PCA(X):

    # RandomBar and Center the data by subtracting the mean
    X_ran = np.random.rand(1, X.shape[1])
    X = np.concatenate((X, X_ran), axis=0)
    # 创建 PCA 模型并拟合数据
    pca = PCA()
    pca.fit(X)

    # 获取投影矩阵
    projection_matrix = pca.components_.T

    # 计算每一行数据在主成分上的投影
    importance = np.dot(X, projection_matrix)
    row_importance = np.sum(importance, axis=1)
    X_new = []
    for i in range(row_importance.shape[0]):
        if row_importance[i] < row_importance[-1]:
            X_new.append(X[i])


    new_sorted_data = np.reshape(X_new,(len(X_new), X.shape[1]))
    # # 按重要程度对样本进行排序
    # sorted_indices = np.argsort(importance[:, 0]).T  # 按第一个主成分排序
    #
    # # result = np.concatenate((X, sorted_indices), axis=1)
    # sorted_data = X[sorted_indices]
    # indices = np.argwhere((sorted_data == X_ran).all(axis=1))
    # # 二维矩阵获取指定位置的数
    # indices = indices[0][0]
    #
    # # 使用切片操作删除指定行数之后的数据
    # new_sorted_data = sorted_data[:indices - 1, :]
    #
    #
    #
    return new_sorted_data
    # # 打印删除后的矩阵
    # print("删除指定行数之后的矩阵：")
    # print(new_sorted_data)




